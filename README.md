# sh: Media - Telegram Bot

Минималистичный Telegram-бот для генерации и редактирования изображений с помощью OpenAI API.

## Возможности

- **Генерация изображений**: `/img <описание>` — создает новое изображение по текстовому описанию
- **Редактирование изображений**: Ответьте на сообщение с изображением командой `/img <описание>` для редактирования
- **Пакетная обработка**: Поддержка нескольких изображений в одном сообщении

## Установка и запуск

### Требования

- Python 3.8+
- Telegram Bot Token
- OpenAI API ключ (уже настроен в проекте)

### Установка зависимостей

```bash
pip install aiogram openai aiohttp
```

### Настройка

1. Получите токен бота у [@BotFather](https://t.me/BotFather) в Telegram
2. Откройте файл `media_config.py`
3. Вставьте ваш токен в переменную `TELEGRAM_BOT_TOKEN`:

```python
TELEGRAM_BOT_TOKEN = "ваш_токен_здесь"
```

### Запуск

```bash
python media_bot.py
```

## Использование

1. Запустите бота командой `/start`
2. Для генерации изображения: `/img кот-космонавт`
3. Для редактирования: ответьте на сообщение с фото командой `/img добавить солнечные очки`

## Структура проекта

```
media_config.py      # Токены и константы
media_bot.py         # Точка входа, Router, запуск
media_handlers.py    # Обработчики команд и сообщений (будет добавлен)
media_openai.py      # Обёртки над images.generate / images.edit (будет добавлен)
media_utils.py       # Вспомогательные функции (будет добавлен)
```

## Статус разработки

- ✅ **Этап 1**: Инициализация проекта
- ✅ **Этап 2**: Генерация изображений
- ✅ **Этап 3**: Редактирование изображений
- ⏳ **Этап 4**: Завершение и деплой

## Лицензия

MIT License
