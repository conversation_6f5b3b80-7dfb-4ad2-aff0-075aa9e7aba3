"""
Обработчики команд и сообщений для Telegram-бота "sh: Media"
"""

import logging
from aiogram import Router
from aiogram.filters import Command
from aiogram.types import Message, BufferedInputFile
from media_openai import generate_image, edit_images
from media_utils import extract_images_from_message, determine_operation_mode, extract_prompt_from_command, format_error_message

logger = logging.getLogger(__name__)

# Создаем роутер для обработчиков
router = Router()


@router.message(Command("img"))
async def cmd_img_handler(message: Message):
    """
    Обработчик команды /img для генерации и редактирования изображений
    Определяет режим работы: генерация или редактирование
    """
    # Извлекаем prompt из команды
    prompt = extract_prompt_from_command(message.text or "")

    # Проверяем, что prompt не пустой
    if not prompt:
        await message.answer(format_error_message("empty_prompt"))
        logger.warning(f"Пользователь {message.from_user.id} отправил пустой prompt")
        return

    # Определяем режим работы
    operation_mode = determine_operation_mode(message)

    if operation_mode == "edit":
        await handle_image_edit(message, prompt)
    else:
        await handle_image_generation(message, prompt)


async def handle_image_generation(message: Message, prompt: str):
    """
    Обработка генерации изображения
    """
    # Отправляем сообщение о начале генерации
    status_message = await message.answer("🎨 Генерирую изображение, подождите...")

    try:
        logger.info(f"Пользователь {message.from_user.id} запросил генерацию: '{prompt}'")

        # Генерируем изображение
        image_bytes = await generate_image(prompt)

        if image_bytes:
            # Создаем файл для отправки
            image_file = BufferedInputFile(
                file=image_bytes,
                filename=f"generated_image.png"
            )

            # Отправляем изображение
            await message.answer_photo(
                photo=image_file,
                caption=f"🎨 Изображение по запросу: {prompt}"
            )

            # Удаляем сообщение о статусе
            await status_message.delete()

            logger.info(f"Изображение успешно отправлено пользователю {message.from_user.id}")

        else:
            # Ошибка генерации
            await status_message.edit_text(format_error_message("api_error"))
            logger.error(f"Не удалось сгенерировать изображение для пользователя {message.from_user.id}")

    except Exception as e:
        # Обработка неожиданных ошибок
        await status_message.edit_text(format_error_message("unknown_error"))
        logger.error(f"Неожиданная ошибка при генерации изображения: {e}")


async def handle_image_edit(message: Message, prompt: str):
    """
    Обработка редактирования изображения
    """
    # Отправляем сообщение о начале редактирования
    status_message = await message.answer("✏️ Редактирую изображение, подождите...")

    try:
        logger.info(f"Пользователь {message.from_user.id} запросил редактирование: '{prompt}'")

        # Извлекаем изображения из сообщения или реплая
        images = await extract_images_from_message(message)

        if not images:
            await status_message.edit_text(
                "❌ Не найдено изображений для редактирования.\n"
                "Убедитесь, что вы отвечаете на сообщение с фото или прикрепили фото к команде.\n"
                "Также проверьте, что размер изображения не превышает 20 МБ."
            )
            return

        # Дополнительная проверка размера изображений
        for i, img_bytes in enumerate(images):
            if len(img_bytes) > 20 * 1024 * 1024:  # 20 MB
                await status_message.edit_text(format_error_message("file_too_large"))
                logger.warning(f"Изображение {i+1} слишком большое: {len(img_bytes)} байт")
                return

        # Редактируем изображение
        edited_image_bytes = await edit_images(prompt, images)

        if edited_image_bytes:
            # Создаем файл для отправки
            image_file = BufferedInputFile(
                file=edited_image_bytes,
                filename=f"edited_image.png"
            )

            # Отправляем отредактированное изображение
            await message.answer_photo(
                photo=image_file,
                caption=f"✏️ Изображение отредактировано: {prompt}"
            )

            # Удаляем сообщение о статусе
            await status_message.delete()

            logger.info(f"Отредактированное изображение успешно отправлено пользователю {message.from_user.id}")

        else:
            # Ошибка редактирования
            await status_message.edit_text(format_error_message("api_error"))
            logger.error(f"Не удалось отредактировать изображение для пользователя {message.from_user.id}")

    except Exception as e:
        # Обработка неожиданных ошибок
        await status_message.edit_text(format_error_message("unknown_error"))
        logger.error(f"Неожиданная ошибка при редактировании изображения: {e}")


@router.message()
async def handle_other_messages(message: Message):
    """
    Обработчик всех остальных сообщений
    Дает подсказки пользователю о том, как использовать бота
    """
    if message.text and not message.text.startswith("/"):
        # Если это обычное текстовое сообщение, даем подсказку
        await message.answer(
            "💡 Для работы с изображениями используйте команду `/img`:\n\n"
            "🎨 **Генерация изображения:**\n"
            "`/img красивый закат над морем`\n\n"
            "✏️ **Редактирование изображения:**\n"
            "• Ответьте на сообщение с фото: `/img добавить солнечные очки`\n"
            "• Или прикрепите фото к команде: `/img изменить цвет на синий`"
        )
    elif message.photo and not message.text:
        # Если прислали только фото без команды
        await message.answer(
            "📸 Вы прислали изображение! Чтобы его отредактировать, используйте команду:\n"
            "`/img <описание изменений>`\n\n"
            "Например: `/img добавить солнечные очки`"
        )
    # Остальные типы сообщений игнорируем
